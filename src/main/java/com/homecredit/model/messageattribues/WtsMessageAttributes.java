package com.homecredit.model.messageattribues;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

@Data
@ToString(callSuper = true)
public class WtsMessageAttributes extends BaseMessageAttributes { //TODO

    @JsonProperty("creative_content")
    private String creativeContent;
    @JsonProperty(value = "creative_id", required = true)
    private String creativeId;
    @JsonProperty(value = "TASKPROP_WTS_time_valid_duration", required = true)
    private String taskpropWtsTimeValidDuration;
    private String recipient;
}
